<view class="chat-container">
  <!-- 顶部导航栏 -->
  <view class="navbar">
    <view class="navbar-content">
      <view class="navbar-title">
        <text class="title">AI助手</text>
        <text class="subtitle">智能对话助手</text>
      </view>

      <view class="navbar-actions">
        <view class="action-item model-selector" bindtap="toggleModelSelector">
          <text class="model-text">{{currentModel || 'GPT-4'}}</text>
          <text class="selector-icon">{{showModelSelector ? '▲' : '▼'}}</text>
        </view>

        <view class="action-item clear-btn" bindtap="clearChat">
          <text class="clear-icon">🗑️</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 模型选择下拉菜单 -->
  <view class="model-dropdown {{showModelSelector ? 'show' : ''}}" wx:if="{{showModelSelector}}">
    <view class="dropdown-content">
      <view class="dropdown-header">
        <text class="dropdown-title">选择AI模型</text>
      </view>
      <view class="model-list">
        <view
          class="model-option {{item === currentModel ? 'selected' : ''}}"
          wx:for="{{availableModels}}"
          wx:key="*this"
          data-model="{{item}}"
          bindtap="switchModel"
        >
          <text class="model-name">{{item}}</text>
          <text class="check-mark" wx:if="{{item === currentModel}}">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 聊天内容区域 -->
  <scroll-view
    class="chat-messages"
    scroll-y
    scroll-into-view="{{scrollToMessage}}"
    bindscrolltoupper="loadMoreMessages"
    enhanced="true"
    show-scrollbar="{{false}}"
    bounces="true"
  >
    <!-- 日期分隔符 -->
    <view class="date-divider">
      <text class="date-text">{{currentDate}}</text>
    </view>

    <!-- 消息列表 -->
    <view class="messages-list">
      <view
        class="message-item {{item.type === 'user' ? 'user-message' : 'ai-message'}}"
        wx:for="{{messages}}"
        wx:key="id"
        id="msg-{{item.id}}"
      >
        <!-- 用户消息 -->
        <view wx:if="{{item.type === 'user'}}" class="user-bubble">
          <text class="message-text">{{item.content}}</text>
          <view class="message-meta">
            <text class="time-stamp">{{item.time}}</text>
          </view>
        </view>

        <!-- AI消息 -->
        <view wx:else class="ai-bubble">
          <view class="ai-avatar">
            <text class="avatar-emoji">🤖</text>
          </view>
          <view class="ai-content">
            <!-- 带代码块的AI消息 -->
            <view wx:if="{{item.parsedContent}}" class="parsed-content">
              <block wx:for="{{item.parsedContent}}" wx:key="index" wx:for-item="block">
                <!-- 普通文本 -->
                <text wx:if="{{!block.isCode}}" class="content-text">{{block.content}}</text>

                <!-- 代码块 -->
                <view wx:else class="code-container">
                  <view class="code-header">
                    <text class="code-lang">{{block.language || 'code'}}</text>
                    <view class="copy-code-btn" bindtap="copyCode" data-code="{{block.content}}">
                      <text class="copy-text">复制</text>
                    </view>
                  </view>
                  <scroll-view class="code-area" scroll-x>
                    <text class="code-text">{{block.content}}</text>
                  </scroll-view>
                </view>
              </block>
            </view>

            <!-- 系统消息 -->
            <view wx:elif="{{item.type === 'info' || item.type === 'warning'}}" class="system-content {{item.type}}">
              <text class="system-text">{{item.content}}</text>
            </view>

            <!-- 普通AI消息 -->
            <text wx:else class="content-text">{{item.content}}</text>

            <!-- AI消息底部 -->
            <view class="ai-footer">
              <text class="time-stamp">{{item.time}}</text>
              <view class="ai-actions">
                <view class="action-copy" bindtap="copyMessage" data-content="{{item.content}}">
                  <text class="action-icon">📋</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- AI正在输入 -->
    <view class="typing-indicator" wx:if="{{isAiTyping}}">
      <view class="ai-avatar">
        <text class="avatar-emoji">🤖</text>
      </view>
      <view class="typing-animation">
        <view class="typing-dots">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
        <text class="typing-text">AI正在思考...</text>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-wrapper">
      <view class="input-field">
        <input
          class="text-input"
          value="{{inputMessage}}"
          bindinput="onInput"
          placeholder="输入您的问题..."
          confirm-type="send"
          bindconfirm="sendMessage"
          focus="{{inputFocus}}"
          maxlength="1000"
        />
      </view>

      <view class="input-controls">
        <view class="control-btn voice-btn" bindtap="startVoiceInput">
          <text class="btn-emoji">🎤</text>
        </view>

        <view
          class="control-btn send-btn {{inputMessage ? 'active' : 'disabled'}}"
          bindtap="sendMessage"
        >
          <text class="btn-emoji">{{inputMessage ? '🚀' : '💬'}}</text>
        </view>
      </view>
    </view>
  </view>
</view>