<view class="container">
  <!-- 顶部导航栏 -->
  <view class="navbar">
    <view class="navbar-content">
      <view class="navbar-title">
        <text class="title">智能翻译</text>
        <text class="subtitle">多语言互译，支持文本和语音</text>
      </view>

      <view class="navbar-actions">
        <view class="model-selector" bindtap="switchModel">
          <text class="model-text">{{selectedModel}}</text>
          <text class="selector-icon">⚙️</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 语言选择区域 -->
  <view class="language-section">
    <view class="language-selectors">
      <view class="language-item" bindtap="showSourceLanguages">
        <text class="language-label">从</text>
        <view class="language-value">
          <text class="language-name">{{sourceLanguage.name}}</text>
          <text class="dropdown-arrow">▼</text>
        </view>
      </view>

      <view class="switch-languages" bindtap="switchLanguages">
        <text class="switch-icon">⇄</text>
      </view>

      <view class="language-item" bindtap="showTargetLanguages">
        <text class="language-label">到</text>
        <view class="language-value">
          <text class="language-name">{{targetLanguage.name}}</text>
          <text class="dropdown-arrow">▼</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <scroll-view scroll-y class="main-content" enhanced show-scrollbar="{{false}}">
    <!-- 输入卡片 -->
    <view class="input-card">
      <view class="card-header">
        <text class="card-title">输入文本</text>
        <view class="card-actions">
          <view class="action-btn voice-btn" bindtouchstart="startVoiceInput" bindtouchend="stopVoiceInput">
            <text class="btn-emoji {{isRecording ? 'recording' : ''}}">🎤</text>
          </view>
          <view class="action-btn clear-btn" bindtap="clearInput" wx:if="{{inputText}}">
            <text class="btn-emoji">🗑️</text>
          </view>
        </view>
      </view>

      <view class="input-area">
        <textarea
          class="input-textarea"
          placeholder="请输入要翻译的文本..."
          value="{{inputText}}"
          bindinput="onInput"
          maxlength="500"
          show-confirm-bar="{{false}}"
          auto-height
        ></textarea>
      </view>

      <view class="input-footer">
        <text class="char-count">{{inputText.length}}/500</text>
        <view class="translate-btn {{inputText ? 'active' : 'disabled'}}" bindtap="translate">
          <text class="btn-text">翻译</text>
          <text class="btn-emoji">🌐</text>
        </view>
      </view>
    </view>

    <!-- 翻译结果卡片 -->
    <view class="result-card" wx:if="{{translatedText}}">
      <view class="card-header">
        <text class="card-title">翻译结果</text>
        <view class="card-actions">
          <view class="action-btn play-btn" bindtap="playTranslation">
            <text class="btn-emoji">🔊</text>
          </view>
          <view class="action-btn copy-btn" bindtap="copyResult">
            <text class="btn-emoji">📋</text>
          </view>
          <view class="action-btn share-btn" bindtap="shareResult">
            <text class="btn-emoji">📤</text>
          </view>
        </view>
      </view>

      <view class="result-content">
        <text class="result-text">{{translatedText}}</text>
      </view>
    </view>

    <!-- 历史记录卡片 -->
    <view class="history-card" wx:if="{{translateHistory.length > 0}}">
      <view class="card-header">
        <text class="card-title">历史记录</text>
        <view class="card-actions">
          <view class="action-btn clear-btn" bindtap="clearHistory">
            <text class="btn-emoji">🗑️</text>
          </view>
        </view>
      </view>

      <view class="history-list">
        <view class="history-item" wx:for="{{translateHistory}}" wx:key="time" bindtap="selectHistoryItem" data-index="{{index}}">
          <view class="history-header">
            <text class="language-pair">{{item.from}} → {{item.to}}</text>
            <view class="history-actions">
              <view class="history-action" catchtap="copyHistoryItem" data-index="{{index}}">
                <text class="action-emoji">📋</text>
              </view>
            </view>
          </view>

          <view class="history-content">
            <text class="source-text">{{item.source}}</text>
            <text class="result-text">{{item.result}}</text>
          </view>

          <view class="history-footer">
            <text class="history-time">{{item.time}}</text>
            <text class="history-model" wx:if="{{item.model}}">{{item.model}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!translateHistory.length && !translatedText}}">
      <text class="empty-icon">🌐</text>
      <text class="empty-title">开始翻译</text>
      <text class="empty-desc">输入文本，体验智能翻译功能</text>
    </view>
  </scroll-view>

  <!-- 语言选择弹窗 -->
  <view class="modal-mask" wx:if="{{showLanguagePicker}}" bindtap="hideLanguagePicker">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">选择语言</text>
        <view class="close-btn" bindtap="hideLanguagePicker">
          <text class="close-icon">✕</text>
        </view>
      </view>

      <scroll-view scroll-y class="language-options">
        <view
          class="language-option {{item.code === (isSelectingSource ? sourceLanguage.code : targetLanguage.code) ? 'selected' : ''}}"
          wx:for="{{languages}}"
          wx:key="code"
          data-language="{{item}}"
          bindtap="selectLanguage"
        >
          <text class="option-text">{{item.name}}</text>
          <text class="check-mark" wx:if="{{item.code === (isSelectingSource ? sourceLanguage.code : targetLanguage.code)}}">✓</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 模型选择弹窗 -->
  <view class="modal-mask" wx:if="{{showModelPicker}}" bindtap="hideModelPicker">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">选择模型</text>
        <view class="close-btn" bindtap="hideModelPicker">
          <text class="close-icon">✕</text>
        </view>
      </view>

      <scroll-view scroll-y class="language-options">
        <view
          class="language-option {{item.name === selectedModel ? 'selected' : ''}}"
          wx:for="{{models}}"
          wx:key="value"
          data-index="{{index}}"
          bindtap="selectModel"
        >
          <text class="option-text">{{item.name}}</text>
          <text class="check-mark" wx:if="{{item.name === selectedModel}}">✓</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{showLoading}}">
    <view class="loading-spinner">
      <view class="spinner-dots">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
      <text class="loading-text">翻译中...</text>
    </view>
  </view>
</view>