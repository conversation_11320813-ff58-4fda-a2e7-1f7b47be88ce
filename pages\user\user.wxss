.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8faff 0%, #f0f4ff 100%);
  padding-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.container::before {
  content: "";
  position: absolute;
  width: 800rpx;
  height: 800rpx;
  background: radial-gradient(circle, rgba(111, 66, 193, 0.05) 0%, rgba(111, 66, 193, 0) 70%);
  border-radius: 50%;
  top: -400rpx;
  right: -200rpx;
  z-index: 0;
  animation: rotate 30s infinite linear;
}

.container::after {
  content: "";
  position: absolute;
  width: 600rpx;
  height: 600rpx;
  background: radial-gradient(circle, rgba(97, 134, 255, 0.05) 0%, rgba(97, 134, 255, 0) 70%);
  border-radius: 50%;
  bottom: -300rpx;
  left: -200rpx;
  z-index: 0;
  animation: rotate 25s infinite linear reverse;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.user-info {
  background: linear-gradient(135deg, #6e45e2, #88d3ce);
  padding: 100rpx 40rpx 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(110, 69, 226, 0.2);
  border-bottom-left-radius: 40rpx;
  border-bottom-right-radius: 40rpx;
  overflow: hidden;
  animation: fadeIn 0.8s ease-out;
}

/* 背景动画元素 */
.user-info::before {
  content: "";
  position: absolute;
  width: 400rpx;
  height: 400rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  top: -200rpx;
  right: -200rpx;
  z-index: 0;
  animation: pulse 8s infinite ease-in-out alternate;
}

.user-info::after {
  content: "";
  position: absolute;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  bottom: -150rpx;
  left: -100rpx;
  z-index: 0;
  animation: pulse 6s infinite ease-in-out alternate-reverse;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.5; }
  100% { transform: scale(1.2); opacity: 0.8; }
}

.avatar-container {
  position: relative;
  width: 220rpx;
  height: 220rpx;
  margin-bottom: 30rpx;
  z-index: 1;
}

.avatar {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 8rpx solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.25), 0 0 0 2rpx rgba(255, 255, 255, 0.2);
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  z-index: 2;
  animation: floatAvatar 6s infinite ease-in-out;
  transform-style: preserve-3d;
  perspective: 1000rpx;
}

.avatar-glow {
  position: absolute;
  width: 220rpx;
  height: 220rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  filter: blur(15rpx);
  z-index: 1;
  animation: glowPulse 3s infinite alternate;
}

@keyframes glowPulse {
  0% { transform: scale(0.9); opacity: 0.5; }
  100% { transform: scale(1.1); opacity: 0.8; }
}

.avatar::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  top: 0;
  left: 0;
  z-index: 2;
  animation: shimmer 3s infinite linear;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
}

.circle-1 {
  width: 180rpx;
  height: 180rpx;
  background: rgba(255, 255, 255, 0.1);
  top: 60rpx;
  right: 60rpx;
  animation: floatCircle 10s infinite ease-in-out;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.08);
  bottom: 80rpx;
  left: 80rpx;
  animation: floatCircle 8s infinite ease-in-out reverse;
}

@keyframes floatCircle {
  0% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
  100% { transform: translateY(0) rotate(360deg); }
}

@keyframes floatAvatar {
  0% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-10rpx) rotate(2deg); }
  50% { transform: translateY(0) rotate(0deg); }
  75% { transform: translateY(10rpx) rotate(-2deg); }
  100% { transform: translateY(0) rotate(0deg); }
}

@keyframes shimmer {
  0% { opacity: 0.5; transform: scale(1) rotate(0deg); }
  50% { opacity: 1; transform: scale(1.02) rotate(180deg); }
  100% { opacity: 0.5; transform: scale(1) rotate(360deg); }
}

.nickname {
  font-size: 44rpx;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
  letter-spacing: 2rpx;
  margin-bottom: 10rpx;
  background: linear-gradient(90deg, #ffffff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: fadeInUp 0.8s ease-out;
}

.user-status {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
  margin-top: 10rpx;
  position: relative;
  z-index: 1;
  animation: fadeInUp 0.8s ease-out;
  animation-delay: 0.2s;
  opacity: 0;
  animation-fill-mode: forwards;
  backdrop-filter: blur(5px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.menu-list {
  background: rgba(255, 255, 255, 0.9);
  margin: 0 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 15rpx 35rpx rgba(110, 69, 226, 0.08);
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  animation: slideUp 0.8s ease-out;
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(30rpx);
  animation-fill-mode: forwards;
  position: relative;
  z-index: 1;
}

/* 菜单背景装饰 */
.menu-list::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0) 100%);
  z-index: -1;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(50rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx;
  border-bottom: 1rpx solid rgba(240, 240, 255, 0.6);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(110, 69, 226, 0.05), rgba(136, 211, 206, 0.05));
  transform: translateX(-100%);
  transition: transform 0.4s ease;
  z-index: 0;
}

.menu-item:active {
  background-color: rgba(240, 240, 255, 0.5);
  transform: scale(0.98);
}

.menu-item:active::before {
  transform: translateX(0);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 84rpx;
  height: 84rpx;
  margin-right: 36rpx;
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, rgba(110, 69, 226, 0.1), rgba(136, 211, 206, 0.1));
  border-radius: 20rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(110, 69, 226, 0.05);
}

.menu-icon {
  width: 52rpx;
  height: 52rpx;
  opacity: 0.85;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.menu-item:active .menu-icon-container {
  transform: scale(1.05);
  background: linear-gradient(135deg, rgba(110, 69, 226, 0.2), rgba(136, 211, 206, 0.2));
}

.menu-item:active .menu-icon {
  transform: scale(1.1);
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.menu-text {
  font-size: 32rpx;
  color: #374151;
  font-weight: 500;
  position: relative;
  z-index: 1;
  letter-spacing: 0.5rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #6B7280;
  margin-top: 6rpx;
  opacity: 0.8;
}

.arrow {
  width: 38rpx;
  height: 38rpx;
  opacity: 0.6;
  transition: all 0.4s ease;
  position: relative;
  z-index: 1;
}

.menu-item:active .arrow {
  transform: translateX(10rpx);
  opacity: 0.9;
}

.logout-button {
  margin: 100rpx 30rpx 50rpx;
  animation: fadeIn 1s ease-out;
  animation-delay: 0.6s;
  opacity: 0;
  animation-fill-mode: forwards;
  position: relative;
}

/* 炫酷发光效果 */
.logout-button::before {
  content: "";
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: linear-gradient(90deg, rgba(255, 154, 158, 0.2) 0%, rgba(255, 106, 136, 0.2) 100%);
  border-radius: 50rpx;
  filter: blur(15rpx);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.logout-button:active::before {
  opacity: 1;
}

.logout-button button {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  font-size: 34rpx;
  border-radius: 48rpx;
  background: linear-gradient(120deg, #ff9a9e 0%, #ff6a88 60%, #fd5d93 100%) !important;
  border: none !important;
  color: #fff;
  font-weight: 600;
  letter-spacing: 3rpx;
  box-shadow: 0 15rpx 25rpx rgba(255, 106, 136, 0.3);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
}

.logout-button button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.logout-button button:active {
  transform: scale(0.97);
  box-shadow: 0 8rpx 15rpx rgba(255, 106, 136, 0.3);
}

.logout-button button:active::after {
  transform: translateX(100%);
}

.button-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer {
  margin-top: 60rpx;
  text-align: center;
  opacity: 0.6;
  animation: fadeIn 1s ease-out;
  animation-delay: 0.8s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.copyright {
  font-size: 24rpx;
  color: #6B7280;
}

/* 添加菜单项的悬停效果 */
.menu-item-hover {
  background-color: rgba(240, 240, 255, 0.5);
  transform: translateX(6rpx);
} 