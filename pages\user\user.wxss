.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 用户信息区域 */
.profile-section {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  padding: 60rpx 24rpx 40rpx;
  position: relative;
}

.profile-card {
  display: flex;
  align-items: center;
  gap: 24rpx;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 32rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.avatar-wrapper {
  position: relative;
  flex-shrink: 0;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.avatar-status {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: #10b981;
  border-radius: 50%;
  border: 3rpx solid #ffffff;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.username {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.2;
}

.user-role {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.user-stats {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 8rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rpx;
}

.stat-number {
  font-size: 24rpx;
  font-weight: 600;
  color: #ffffff;
}

.stat-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.stat-divider {
  width: 1rpx;
  height: 32rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 24rpx;
  background-color: #f8fafc;
}

/* 功能菜单区域 */
.menu-section {
  margin-bottom: 32rpx;
}

.section-header {
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a202c;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.menu-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1rpx solid #f1f5f9;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
  text-decoration: none;
}

.menu-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border-color: #e2e8f0;
}

.menu-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  background-color: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.icon-emoji {
  font-size: 32rpx;
}

.menu-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4rpx;
}

.menu-desc {
  font-size: 20rpx;
  color: #64748b;
  line-height: 1.4;
}

/* 其他选项区域 */
.options-section {
  margin-bottom: 32rpx;
}

.options-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  border: 1rpx solid #f1f5f9;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f1f5f9;
  transition: all 0.2s ease;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item:active {
  background-color: #f8fafc;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.option-icon {
  font-size: 28rpx;
}

.option-text {
  font-size: 26rpx;
  color: #374151;
  font-weight: 500;
}

.option-arrow {
  font-size: 32rpx;
  color: #9ca3af;
  font-weight: 300;
}

/* 退出登录区域 */
.logout-section {
  margin: 32rpx 0;
}

.logout-btn {
  background-color: #ef4444;
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.2);
}

.logout-btn:active {
  background-color: #dc2626;
  transform: scale(0.98);
}

.logout-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 底部信息 */
.footer-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 40rpx 0;
  margin-top: 32rpx;
}

.version-info {
  font-size: 22rpx;
  color: #9ca3af;
}

.copyright {
  font-size: 20rpx;
  color: #9ca3af;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .profile-section {
    padding: 40rpx 16rpx 32rpx;
  }

  .main-content {
    padding: 16rpx;
  }

  .menu-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
}