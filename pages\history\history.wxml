<view class="container">
  <!-- 顶部导航栏 -->
  <view class="navbar">
    <view class="navbar-content">
      <view class="navbar-title">
        <text class="title">使用记录</text>
        <text class="subtitle">{{historyList.length}}条记录</text>
      </view>

      <!-- 状态指示器 -->
      <view class="status-indicators">
        <!-- 网络状态 -->
        <view class="status-item network-status {{networkStatus.available ? 'online' : 'offline'}}">
          <view class="status-dot"></view>
        </view>

        <!-- 游客模式提示 -->
        <view class="status-item guest-mode" wx:if="{{isGuestMode}}">
          <text class="guest-text">游客</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选和统计区域 -->
  <view class="filter-section">
    <scroll-view scroll-x class="filter-tabs" show-scrollbar="{{false}}" enhanced>
      <view
        class="filter-tab {{activeTab === item.id ? 'active' : ''}}"
        wx:for="{{tabs}}"
        wx:key="id"
        data-tab="{{item.id}}"
        bindtap="switchTab"
        hover-class="filter-tab-hover"
      >
        <text class="tab-name">{{item.name}}</text>
        <view class="tab-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
      </view>
    </scroll-view>

    <!-- 更新时间和操作 -->
    <view class="section-footer">
      <view class="update-time">
        <text>{{lastRefreshTime}}</text>
      </view>
      <view class="quick-actions" wx:if="{{historyList.length > 0 && !isGuestMode}}">
        <view class="action-item" bindtap="clearAllHistory">
          <text class="action-text">清空</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 主内容区域 -->
  <scroll-view
    scroll-y
    class="content-area"
    enable-flex
    refresher-enabled="{{true}}"
    refresher-triggered="{{loading}}"
    bindrefresherrefresh="onPullDownRefresh"
    enhanced
    show-scrollbar="{{false}}"
    bounces="{{true}}"
  >
    <!-- 离线模式提示 -->
    <view class="notice-bar offline" wx:if="{{!networkStatus.available}}">
      <view class="notice-icon">⚠️</view>
      <text class="notice-text">离线模式，部分功能受限</text>
    </view>

    <!-- 游客模式提示 -->
    <view class="notice-bar guest" wx:if="{{isGuestMode}}">
      <view class="notice-icon">👤</view>
      <text class="notice-text">游客模式，记录不会保存</text>
      <navigator url="/pages/login/login" open-type="redirect" class="notice-action">登录</navigator>
    </view>

    <!-- 历史记录列表 -->
    <view class="history-list" wx:if="{{historyList.length > 0}}">
      <view
        class="history-card"
        wx:for="{{historyList}}"
        wx:key="id"
        hover-class="card-hover"
        hover-stay-time="100"
        bindtap="viewDetail"
        data-index="{{index}}"
      >
        <!-- 卡片头部 -->
        <view class="card-header">
          <view class="type-indicator {{item.type}}">
            <text class="type-icon">{{item.type === 'chat' ? '💬' : item.type === 'translate' ? '🌐' : item.type === 'image' ? '🖼️' : '🎤'}}</text>
            <text class="type-name">{{item.typeText || (item.type === 'chat' ? '对话' : item.type === 'translate' ? '翻译' : item.type === 'image' ? '图像' : '语音')}}</text>
          </view>

          <view class="card-actions" wx:if="{{!isGuestMode}}">
            <view class="action-delete" catchtap="deleteItem" data-index="{{index}}">
              <text class="delete-icon">🗑️</text>
            </view>
          </view>
        </view>

        <!-- 卡片内容 -->
        <view class="card-content">
          <view class="content-main">
            <text class="content-query">{{item.query || item.content || '无内容'}}</text>
            <text class="content-result" wx:if="{{item.result}}">{{item.result.length > 80 ? item.result.substring(0, 80) + '...' : item.result}}</text>
          </view>

          <!-- 图片预览 -->
          <view class="content-image" wx:if="{{item.type === 'image' && item.imagePath}}">
            <image src="{{item.imagePath}}" mode="aspectFill" class="image-thumb" binderror="handleImageError" data-index="{{index}}"></image>
          </view>
        </view>

        <!-- 卡片底部 -->
        <view class="card-footer">
          <view class="footer-info">
            <text class="time-text">{{item.time || '未知时间'}}</text>
            <text class="model-text" wx:if="{{item.model}}">{{item.model}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && isEmpty}}">
      <view class="empty-icon">📝</view>
      <text class="empty-title">暂无{{activeTab === 'all' ? '' : currentTabName}}记录</text>
      <text class="empty-desc">开始使用AI助手，记录将显示在这里</text>
      <navigator url="/pages/index/index" open-type="switchTab" class="start-btn">开始使用</navigator>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-dots">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view>