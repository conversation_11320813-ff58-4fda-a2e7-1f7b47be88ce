.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  position: relative;
}

/* 添加背景动画元素 */
.container::before {
  content: "";
  position: absolute;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  top: -100rpx;
  left: -100rpx;
  animation: float 8s infinite ease-in-out;
}

.container::after {
  content: "";
  position: absolute;
  width: 400rpx;
  height: 400rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  bottom: -200rpx;
  right: -150rpx;
  animation: float 10s infinite ease-in-out reverse;
}

@keyframes float {
  0% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(5deg); }
  100% { transform: translateY(0) rotate(0deg); }
}

.header {
  margin-top: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 1s ease-out;
  z-index: 1;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-50rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.logo {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
  border-radius: 50%;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  animation: pulse 3s infinite;
  background: rgba(255, 255, 255, 0.2);
  padding: 10rpx;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.title {
  font-size: 52rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: 2rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 60rpx;
}

.login-box {
  width: 100%;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);
  animation: slideUp 0.8s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(50rpx);
  z-index: 1;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(50rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.mode-title {
  font-size: 32rpx;
  color: #ffffff;
  text-align: center;
  margin-bottom: 30rpx;
  font-weight: 500;
}

.login-btn {
  width: 100% !important;
  height: 90rpx !important;
  line-height: 90rpx !important;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%) !important;
  border-radius: 45rpx !important;
  font-size: 32rpx;
  color: #fff;
  margin-bottom: 16rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
  box-shadow: 0 10rpx 20rpx rgba(79, 172, 254, 0.3);
  transition: all 0.3s ease;
  display: flex !important;
  align-items: center;
  justify-content: center;
  position: relative;
}

.login-btn:active {
  transform: scale(0.98);
  box-shadow: 0 5rpx 10rpx rgba(79, 172, 254, 0.3);
}

.btn-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.btn-tag {
  position: absolute;
  right: 20rpx;
  font-size: 22rpx;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.mode-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 30rpx;
}

.guest-btn {
  width: 100% !important;
  height: 90rpx !important;
  line-height: 90rpx !important;
  background: rgba(255, 255, 255, 0.25) !important;
  backdrop-filter: blur(5px);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 45rpx !important;
  font-size: 32rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
  transition: all 0.3s ease;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.guest-btn:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.35) !important;
}

.privacy-policy {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-top: 30rpx;
}

.link {
  color: #ffffff;
  text-decoration: underline;
  font-weight: 500;
}

.app-features {
  margin-top: 60rpx;
  width: 100%;
  z-index: 1;
  animation: fadeIn 1s ease-out;
  animation-delay: 0.8s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.feature-title {
  font-size: 32rpx;
  color: #ffffff;
  text-align: center;
  margin-bottom: 30rpx;
  font-weight: 500;
}

.feature-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.feature-item {
  width: 48%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(5px);
  border-radius: 20rpx;
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.2);
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.feature-item text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
} 