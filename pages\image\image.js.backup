const app = getApp()

Page({
  data: {
    imagePath: '',
    recognitionResult: '',
    imageHistory: [],
    showLoading: false,
    modelList: [
      { name: 'gemma3:12b', label: 'Gemma 3 (高精度)' },
      { name: 'deepseek-r1:14b', label: 'DeepSeek 14B (通用)' },
      { name: 'deepseek-r1:8b', label: 'DeepSeek 8B (快速)' }
    ],
    selectedModel: 'gemma3:12b', // 默认使用新部署的模型
    processingProgress: 0,
    showProgressBar: false
  },

  onLoad() {
    console.log('图像页面加载');
    
    // 设置初始默认值以避免未定义错误
    this.setData({
      modelList: [
        { name: 'gemma3:12b', label: 'Gemma 3 (高精度)' },
        { name: 'deepseek-r1:14b', label: 'DeepSeek 14B (通用)' },
        { name: 'deepseek-r1:8b', label: 'DeepSeek 8B (快速)' }
      ],
      selectedModel: 'gemma3:12b'
    });
    
    try {
      // 加载历史记录
      const history = wx.getStorageSync('imageHistory') || []
      this.setData({ imageHistory: history })
      
      // 检查可用模型并设置默认模型
      this.checkAvailableModels();
    } catch (error) {
      console.error('页面加载出错:', error);
      wx.showToast({
        title: '页面初始化错误',
        icon: 'none'
      });
    }
  },
  
  // 检查可用模型
  checkAvailableModels() {
    try {
      const baseUrl = app.globalData.apiBaseUrl;
      
      if (!baseUrl) {
        console.error('API基础URL未定义');
        return;
      }
      
      wx.request({
        url: `${baseUrl}/api/tags`,
        timeout: 5000,
        success: (res) => {
          try {
            if (res.data && res.data.models) {
              const availableModels = res.data.models.map(model => model.name);
              console.log('可用模型:', availableModels);
              
              // 更新可用模型列表
              const updatedModelList = this.data.modelList.map(model => {
                return {
                  ...model,
                  available: availableModels.includes(model.name)
                };
              });
              
              // 查找是否有gemma3:12b模型可用
              const hasGemma = availableModels.includes('gemma3:12b');
              
              // 设置默认选择的模型
              let defaultModel = 'gemma3:12b';
              if (!hasGemma) {
                // 如果没有gemma，尝试选择deepseek模型
                if (availableModels.includes('deepseek-r1:14b')) {
                  defaultModel = 'deepseek-r1:14b';
                } else if (availableModels.includes('deepseek-r1:8b')) {
                  defaultModel = 'deepseek-r1:8b';
                }
              }
              
              // 确保选择的是已安装的模型
              console.log(`将使用模型: ${defaultModel}`);
              wx.showToast({
                title: `使用模型: ${defaultModel}`,
                icon: 'none',
                duration: 2000
              });
              
              this.setData({ 
                modelList: updatedModelList,
                selectedModel: defaultModel
              });
            } else {
              console.warn('API响应中没有可用模型');
            }
          } catch (error) {
            console.error('处理API响应出错:', error);
          }
        },
        fail: (error) => {
          console.error('获取模型列表失败:', error);
          
          // 使用默认模型
          const availableModels = ['gemma3:12b', 'deepseek-r1:14b', 'deepseek-r1:8b'];
          this.setData({
            selectedModel: 'gemma3:12b'
          });
          
          wx.showToast({
            title: '无法获取模型列表，使用默认模型',
            icon: 'none',
            duration: 2000
          });
        }
      });
    } catch (error) {
      console.error('模型检查出错:', error);
    }
  },

  // 选择图片
  chooseImage(e) {
    const source = e.currentTarget.dataset.source
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: [source],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath
        this.setData({ 
          imagePath: tempFilePath,
          showLoading: true,
          processingProgress: 0,
          showProgressBar: true
        })
        
        // 模拟进度条增长
        this.startProgressSimulation();
        
        // 将图片转换为Base64并进行识别
        this.convertImageToBase64(tempFilePath);
      }
    })
  },
  
  // 模拟进度条增长
  startProgressSimulation() {
    let progress = 0;
    const simulateProgress = () => {
      // 随机增加1-5%的进度
      progress += Math.random() * 4 + 1;
      
      // 确保不超过95%（最后5%留给实际完成）
      if (progress > 95) progress = 95;
      
      this.setData({ processingProgress: progress });
      
      // 继续模拟，直到接近完成
      if (progress < 95 && this.data.showProgressBar) {
        setTimeout(simulateProgress, 300);
      }
    };
    
    simulateProgress();
  },
  
  // 完成进度条
  completeProgress() {
    this.setData({ 
      processingProgress: 100,
      showProgressBar: false 
    });
  },
  
  // 切换模型
  switchModel() {
    // 获取当前可用的模型
    const availableModels = this.data.modelList.filter(model => model.available !== false);
    
    if (availableModels.length <= 1) {
      wx.showToast({
        title: '没有其他可用模型',
        icon: 'none'
      });
      return;
    }
    
    // 查找当前选中模型的索引
    const currentIndex = availableModels.findIndex(model => model.name === this.data.selectedModel);
    const nextIndex = (currentIndex + 1) % availableModels.length;
    const nextModel = availableModels[nextIndex].name;
    
    this.setData({ selectedModel: nextModel });
    
    wx.showToast({
      title: `已切换到${availableModels[nextIndex].label}`,
      icon: 'none'
    });
  },
  
  // 将图片转换为Base64
  convertImageToBase64(imagePath) {
    try {
      const fs = wx.getFileSystemManager();
      const base64 = fs.readFileSync(imagePath, 'base64');
      
      // 调用图像识别API
      this.recognizeImage(imagePath, base64);
    } catch (error) {
      console.error('转换图片失败:', error);
      wx.showToast({
        title: '图片处理失败',
        icon: 'error'
      });
      this.setData({ 
        showLoading: false,
        showProgressBar: false
      });
    }
  },
  
  // 识别图像
  recognizeImage(imagePath, imageBase64) {
    console.log(`开始图像识别请求 (使用 ${this.data.selectedModel} 模型)`);
    
    // 使用Ollama API进行图像识别
    const baseUrl = app.globalData.apiBaseUrl;
    
    // 根据不同的模型使用不同的API端点
    let ollamaUrl = `${baseUrl}/api/generate`;
    let requestData = {};
    
    // 获取模型信息
    const selectedModelInfo = this.data.modelList.find(m => m.name === this.data.selectedModel);
    
    // 准备提示词，告诉模型这是一个图像分析任务
    let prompt = '';
    
    if (this.data.selectedModel === 'gemma3:12b') {
      // 对于Gemma模型，使用更适合的提示词
      prompt = `分析这张图片并详细描述其中包含的内容。使用中文回答。
      
[图片数据:data:image/jpeg;base64,${imageBase64}]

请尽可能详细地描述图片中的:
1. 主要对象/人物
2. 场景/背景
3. 颜色/氛围
4. 可见的文字/标志
5. 任何其他重要细节`;
    } else {
      // 对于其他模型，使用原来的提示词
      prompt = `
      这是一张图片的Base64编码: data:image/jpeg;base64,${imageBase64.substring(0, 100)}... [图片编码太长已省略]
      
      请详细描述这张图片中包含的内容。使用中文回答。
      注意：即使你可能无法完全解析Base64编码的图片，也请尽量基于上下文推测并提供一个合理的图片描述。
    `;
    }
    
    // 根据不同模型准备请求数据
    if (this.data.selectedModel === 'gemma3:12b') {
      try {
        // 使用Ollama vision API
        ollamaUrl = `${baseUrl}/api/chat`;
        requestData = {
          model: this.data.selectedModel,
          messages: [
            {
              role: "user",
              content: [
                { type: "text", text: "请详细描述这张图片中的内容，使用中文回答。" },
                { 
                  type: "image_url", 
                  image_url: { 
                    url: `data:image/jpeg;base64,${imageBase64}`
                  }
                }
              ]
            }
          ],
          stream: false,
          options: {
            temperature: 0.3,  // 降低温度以提高准确性
            num_predict: 2000  // 增加输出长度以获取更详细的描述
          }
        };
        
        console.log('使用多模态API进行图像识别');
        
      } catch (error) {
        console.error('构建多模态请求时出错:', error);
        
        // 如果多模态请求出错，回退到普通API
        console.log('回退到普通API');
        ollamaUrl = `${baseUrl}/api/generate`;
        requestData = {
          model: this.data.selectedModel,
          prompt: prompt,
          stream: false,
          options: {
            temperature: 0.7,
            num_predict: 2000
          }
        };
      }
    } else {
      // 使用传统的generate API
      requestData = {
        model: this.data.selectedModel,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.7,
          num_predict: 2000
        }
      };
    }
    
    // 显示请求细节
    console.log(`请求API: ${ollamaUrl}`);
    console.log(`使用模型: ${this.data.selectedModel}`);
    
    wx.request({
      url: ollamaUrl,
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: requestData,
      timeout: 60000,
      success: (res) => {
        try {
          console.log('Ollama API响应:', res.data);
          
          // 解析Ollama格式的响应
          let description = '';
          
          if (this.data.selectedModel === 'gemma3:12b' && ollamaUrl.includes('/api/chat')) {
            // 解析chat API响应
            if (res.data && res.data.message && res.data.message.content) {
              description = res.data.message.content;
            } else {
              throw new Error('无法解析识别结果');
            }
          } else {
            // 解析传统API响应
            if (res.data && res.data.response) {
              description = res.data.response.trim();
            } else {
              throw new Error('无法解析识别结果');
            }
          }
          
          // 处理响应中可能包含的"无法解析图片"信息
          if (description.includes('无法解析') || description.includes('无法识别') || description.includes('不能识别') || description.includes('无法处理图像')) {
            description = "识别结果: 当前模型无法直接解析图片内容。\n\n建议:\n1. 检查Ollama版本是否支持多模态\n2. 确认gemma3:12b模型支持图像识别\n3. 尝试使用其他支持图像的模型";
          }
          
          const now = new Date();
          
          // 完成进度条
          this.completeProgress();
          
          // 更新识别结果
          this.setData({
            recognitionResult: description,
            imageHistory: [{
              imagePath: imagePath,
              result: description,
              time: this.formatTime(now),
              model: this.data.selectedModel
            }, ...this.data.imageHistory].slice(0, 10) // 只保留最近10条记录
          });
          
          // 保存到本地存储
          wx.setStorageSync('imageHistory', this.data.imageHistory);
          
        } catch (error) {
          console.error('识别结果处理失败:', error);
          wx.showToast({
            title: '识别结果解析失败',
            icon: 'error'
          });
        }
      },
      fail: (error) => {
        console.error('Ollama API调用失败:', error);
        
        // 尝试重新检测服务连接
        app.checkOllamaService();
        
        wx.showModal({
          title: '连接错误',
          content: '无法连接到Ollama服务，请确保服务已启动: ' + (error.errMsg || '未知错误'),
          showCancel: false
        });
      },
      complete: () => {
        this.setData({ 
          showLoading: false,
          showProgressBar: false
        });
      }
    });
  },

  // 预览图片
  previewImage() {
    if (this.data.imagePath) {
      wx.previewImage({
        urls: [this.data.imagePath]
      })
    }
  },

  // 复制识别结果
  copyResult() {
    wx.setClipboardData({
      data: this.data.recognitionResult,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  },

  // 清空历史记录
  clearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空所有记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            imageHistory: []
          })
          wx.setStorageSync('imageHistory', [])
        }
      }
    })
  },

  // 选择历史记录项
  selectHistoryItem(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.imageHistory[index]
    this.setData({
      imagePath: item.imagePath,
      recognitionResult: item.result
    })
  },
  
  // 复制历史记录项内容
  copyHistoryItem(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.imageHistory[index]
    
    wx.setClipboardData({
      data: item.result,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
    
    // 阻止事件冒泡
    return false;
  },
  
  // 重置图片和识别结果
  resetImage() {
    this.setData({
      imagePath: '',
      recognitionResult: ''
    })
  },
  
  // 翻译识别结果
  translateResult() {
    if (!this.data.recognitionResult) return;
    
    wx.navigateTo({
      url: `/pages/translate/translate?text=${encodeURIComponent(this.data.recognitionResult)}`
    });
  },
  
  // 格式化时间
  formatTime(date) {
    const hour = date.getHours()
    const minute = date.getMinutes()
    
    return `${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}`
  }
}) 