/**index.wxss**/
page {
  height: 100vh;
  background-color: #f8fafc;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;
}

/* 动态背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 60%;
  right: 20%;
  animation-delay: 4s;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40rpx);
  animation: pulse 4s ease-in-out infinite alternate;
}

.orb-1 {
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.3) 0%, rgba(255, 107, 107, 0) 70%);
  top: 20%;
  left: -10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 250rpx;
  height: 250rpx;
  background: radial-gradient(circle, rgba(78, 205, 196, 0.3) 0%, rgba(78, 205, 196, 0) 70%);
  bottom: 10%;
  right: -5%;
  animation-delay: 2s;
}

.light-beam {
  position: absolute;
  width: 2rpx;
  height: 200rpx;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
  animation: beam 3s ease-in-out infinite;
}

.beam-1 {
  top: 30%;
  left: 20%;
  animation-delay: 0s;
}

.beam-2 {
  top: 50%;
  right: 30%;
  animation-delay: 1.5s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-30rpx) rotate(180deg); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.5; }
  100% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes beam {
  0%, 100% { opacity: 0; transform: scaleY(0); }
  50% { opacity: 1; transform: scaleY(1); }
}

/* 炫酷顶部导航栏 */
.hero-navbar {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 100;
  overflow: hidden;
}

.navbar-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
  animation: glow-sweep 3s ease-in-out infinite;
}

.navbar-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particle-float 4s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle-2 {
  top: 60%;
  left: 30%;
  animation-delay: 1s;
}

.particle-3 {
  top: 40%;
  right: 20%;
  animation-delay: 2s;
}

.particle-4 {
  bottom: 30%;
  right: 10%;
  animation-delay: 3s;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 24rpx 32rpx;
  min-height: 120rpx;
  position: relative;
  z-index: 2;
}

.brand-advanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.brand-container {
  position: relative;
  margin-bottom: 8rpx;
}

.brand-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}

.title-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  filter: blur(10rpx);
  z-index: 1;
  animation: title-glow 2s ease-in-out infinite alternate;
}

.title-underline {
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
  border-radius: 2rpx;
  animation: underline-expand 2s ease-in-out infinite;
}

.brand-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  background: #10b981;
  border-radius: 50%;
  animation: status-pulse 2s ease-in-out infinite;
}

.status-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

@keyframes glow-sweep {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0) scale(1); opacity: 0.6; }
  50% { transform: translateY(-20rpx) scale(1.2); opacity: 1; }
}

@keyframes title-glow {
  0% { opacity: 0.5; transform: scale(1); }
  100% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes underline-expand {
  0%, 100% { width: 80rpx; }
  50% { width: 120rpx; }
}

@keyframes status-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 0 24rpx;
  background: transparent;
  position: relative;
  z-index: 1;
}

/* 炫酷欢迎卡片 */
.welcome-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin: 24rpx 0;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  animation: card-float 6s ease-in-out infinite;
}

.welcome-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  animation: shimmer 3s ease-in-out infinite;
}

.welcome-content {
  padding: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 12rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.welcome-desc {
  font-size: 28rpx;
  color: #475569;
  line-height: 1.6;
}

.welcome-action {
  margin-left: 24rpx;
}

.quick-start-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 20rpx 32rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  position: relative;
  overflow: hidden;
}

.quick-start-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transition: left 0.5s ease;
}

.quick-start-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.quick-start-btn:active::before {
  left: 100%;
}

@keyframes card-float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8rpx); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%) skewX(-15deg); }
  100% { transform: translateX(200%) skewX(-15deg); }
}

.btn-text {
  font-size: 26rpx;
  font-weight: 500;
}

.btn-icon {
  font-size: 28rpx;
}

/* 功能区域 */
.features-section {
  margin: 24rpx 0;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8rpx;
  display: block;
}

.section-subtitle {
  font-size: 24rpx;
  color: #64748b;
}

/* 功能网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.feature-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-hover {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
}

.feature-hover::before {
  opacity: 1;
}

.feature-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover .feature-icon::before {
  opacity: 1;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-icon.chat {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}
.feature-icon.voice {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4rpx 16rpx rgba(240, 147, 251, 0.3);
}
.feature-icon.image {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.3);
}
.feature-icon.translate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  box-shadow: 0 4rpx 16rpx rgba(67, 233, 123, 0.3);
}
.feature-icon.music {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  box-shadow: 0 4rpx 16rpx rgba(250, 112, 154, 0.3);
}
.feature-icon.history {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 4rpx 16rpx rgba(168, 237, 234, 0.3);
}

.icon-emoji {
  font-size: 48rpx;
  line-height: 1;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
}

.feature-card:hover .icon-emoji {
  transform: scale(1.1);
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.3));
}

.feature-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.feature-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.feature-desc {
  font-size: 24rpx;
  color: #475569;
  line-height: 1.5;
  opacity: 0.9;
}

/* 调试区域 */
.debug-section {
  margin: 32rpx 0;
}

.debug-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #f1f5f9;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
}

.debug-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 16rpx;
  display: block;
}

.debug-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.debug-btn {
  background-color: #f1f5f9;
  color: #475569;
  border: 1rpx solid #e2e8f0;
  border-radius: 12rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.debug-btn:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.debug-status {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .navbar-content {
    padding: 24rpx 16rpx 20rpx;
  }

  .main-content {
    padding: 0 16rpx;
  }

  .welcome-content {
    padding: 24rpx;
    flex-direction: column;
    text-align: center;
  }

  .welcome-action {
    margin-left: 0;
    margin-top: 20rpx;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  animation: fadeInUp 0.3s ease-out;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }
