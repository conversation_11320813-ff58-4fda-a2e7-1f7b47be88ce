/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  width: 80%;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}

.container {
  padding: 20rpx;
  background: linear-gradient(180deg, #EEF2FF 0%, #F9FAFF 100%);
  min-height: 100vh;
  overflow: hidden;
  position: relative;
}

/* 装饰元素 */
.container::before {
  content: "";
  position: absolute;
  width: 600rpx;
  height: 600rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 70%);
  top: -300rpx;
  right: -300rpx;
  z-index: 0;
}

.container::after {
  content: "";
  position: absolute;
  width: 400rpx;
  height: 400rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 100, 150, 0.08) 0%, rgba(255, 100, 150, 0) 70%);
  bottom: -200rpx;
  left: -100rpx;
  z-index: 0;
}

.header {
  padding: 40rpx 20rpx 60rpx;
  text-align: center;
  position: relative;
  z-index: 1;
  animation: fadeInDown 0.8s ease-out;
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.title {
  font-size: 52rpx;
  font-weight: bold;
  background: linear-gradient(90deg, #3B82F6, #6366F1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16rpx;
  display: block;
  letter-spacing: 2rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #6B7280;
  display: block;
  letter-spacing: 1rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #3B4256;
  margin: 30rpx 10rpx 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #6366F1;
  position: relative;
  z-index: 1;
}

/* 美化的banner样式 */
.banner {
  position: relative;
  height: 340rpx;
  margin: 20rpx 0 50rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.7s ease-out;
  z-index: 1;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(40rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.banner-image {
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.banner:hover .banner-image {
  transform: scale(1.05);
}

.banner-text {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 40rpx 30rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  transform: translateY(0);
  transition: transform 0.3s ease;
}

.banner:active .banner-text {
  transform: translateY(-10rpx);
}

.banner-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 12rpx;
  display: block;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.banner-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 24rpx;
  display: block;
}

.start-chat-btn {
  background: linear-gradient(90deg, #3B82F6, #6366F1);
  color: #fff;
  font-size: 28rpx;
  padding: 14rpx 36rpx;
  border-radius: 36rpx;
  border: none;
  display: inline-block;
  line-height: 1.5;
  box-shadow: 0 5rpx 15rpx rgba(59, 130, 246, 0.4);
  transition: all 0.3s;
}

.start-chat-btn:active {
  transform: translateY(3rpx);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.4);
}

.card-scroll {
  height: 65vh;
  width: 100%;
}

.card-list {
  padding: 10rpx;
  position: relative;
  z-index: 1;
}

.card {
  background: #fff;
  border-radius: 20rpx;
  padding: 36rpx 30rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.06);
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  animation: fadeInRight 0.5s ease-out forwards;
  opacity: 0;
}

.card:nth-child(1) {
  animation-delay: 0.1s;
}

.card:nth-child(2) {
  animation-delay: 0.2s;
}

.card:nth-child(3) {
  animation-delay: 0.3s;
}

.card:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes fadeInRight {
  from { opacity: 0; transform: translateX(30rpx); }
  to { opacity: 1; transform: translateX(0); }
}

.card::before {
  content: "";
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(120deg, rgba(99, 102, 241, 0.05) 0%, rgba(99, 102, 241, 0) 70%);
  border-radius: 50%;
  top: -100rpx;
  right: -100rpx;
  z-index: 0;
  transition: all 0.5s ease;
}

.card-hover {
  background-color: #f5f7ff;
  transform: translateY(-6rpx);
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1);
}

.card-hover::before {
  transform: scale(1.5);
}

.card-icon {
  width: 90rpx;
  height: 90rpx;
  margin-right: 34rpx;
  padding: 16rpx;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 18rpx;
  transition: all 0.3s ease;
}

.card-hover .card-icon {
  background: rgba(99, 102, 241, 0.15);
  transform: scale(1.1);
}

.card-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.card-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 10rpx;
  display: block;
}

.card-desc {
  font-size: 26rpx;
  color: #6B7280;
  display: block;
  line-height: 1.5;
}

.card-arrow {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.card-hover .card-arrow {
  opacity: 0.8;
  transform: translateX(6rpx);
}

.welcome {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  padding: 20rpx 0;
}

.feature-item {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.feature-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.feature-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.quick-start {
  margin-top: 60rpx;
  padding: 0 40rpx;
}

.start-btn {
  width: 100% !important;
  height: 88rpx;
  line-height: 88rpx;
  background: #3A7FED !important;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
}

/* 调试区域样式 */
.debug-section {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.debug-btn {
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background-color: #e6f7ff;
  color: #1677ff;
  border: 1px solid #91d5ff;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.debug-status {
  font-size: 24rpx;
  color: #666;
}
