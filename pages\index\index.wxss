/**index.wxss**/
page {
  height: 100vh;
  background-color: #f8fafc;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.navbar {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e2e8f0;
  position: relative;
  z-index: 100;
}

.navbar-content {
  padding: 32rpx 24rpx 24rpx;
}

.brand {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 24rpx;
  color: #64748b;
  margin-top: 4rpx;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 0 24rpx;
  background-color: #f8fafc;
}

/* 欢迎卡片 */
.welcome-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin: 24rpx 0;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f1f5f9;
}

.welcome-content {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8rpx;
  display: block;
}

.welcome-desc {
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.5;
}

.welcome-action {
  margin-left: 24rpx;
}

.quick-start-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #3b82f6;
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
}

.quick-start-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
}

.btn-text {
  font-size: 26rpx;
  font-weight: 500;
}

.btn-icon {
  font-size: 28rpx;
}

/* 功能区域 */
.features-section {
  margin: 24rpx 0;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8rpx;
  display: block;
}

.section-subtitle {
  font-size: 24rpx;
  color: #64748b;
}

/* 功能网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.feature-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1rpx solid #f1f5f9;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
  text-decoration: none;
}

.feature-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border-color: #e2e8f0;
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  transition: all 0.2s ease;
}

.feature-icon.chat { background-color: #dbeafe; }
.feature-icon.voice { background-color: #fef3c7; }
.feature-icon.image { background-color: #e0e7ff; }
.feature-icon.translate { background-color: #d1fae5; }
.feature-icon.music { background-color: #fce7f3; }
.feature-icon.history { background-color: #f3f4f6; }

.icon-emoji {
  font-size: 40rpx;
  line-height: 1;
}

.feature-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4rpx;
}

.feature-desc {
  font-size: 22rpx;
  color: #64748b;
  line-height: 1.4;
}

/* 调试区域 */
.debug-section {
  margin: 32rpx 0;
}

.debug-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #f1f5f9;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
}

.debug-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 16rpx;
  display: block;
}

.debug-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.debug-btn {
  background-color: #f1f5f9;
  color: #475569;
  border: 1rpx solid #e2e8f0;
  border-radius: 12rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.debug-btn:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.debug-status {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .navbar-content {
    padding: 24rpx 16rpx 20rpx;
  }

  .main-content {
    padding: 0 16rpx;
  }

  .welcome-content {
    padding: 24rpx;
    flex-direction: column;
    text-align: center;
  }

  .welcome-action {
    margin-left: 0;
    margin-top: 20rpx;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  animation: fadeInUp 0.3s ease-out;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }
