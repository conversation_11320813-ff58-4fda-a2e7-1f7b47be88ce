.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.navbar {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e2e8f0;
  position: relative;
  z-index: 100;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx 24rpx;
  min-height: 88rpx;
}

.navbar-title {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.2;
}

.subtitle {
  font-size: 22rpx;
  color: #64748b;
  margin-top: 2rpx;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f1f5f9;
  transition: all 0.2s ease;
}

.model-selector:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.model-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
}

.selector-icon {
  font-size: 20rpx;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 24rpx;
  background-color: #f8fafc;
}

/* 卡片样式 */
.upload-card, .analysis-card, .result-card, .history-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f1f5f9;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.card-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a202c;
}

.card-subtitle {
  font-size: 22rpx;
  color: #64748b;
}

.card-actions {
  display: flex;
  gap: 12rpx;
}

.card-action {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  background-color: #f8fafc;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #64748b;
  transition: all 0.2s ease;
}

.card-action:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

/* 预览区域 */
.preview-area {
  height: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 24rpx;
  border-radius: 12rpx;
  background-color: #f8fafc;
  border: 2rpx dashed #e2e8f0;
  transition: all 0.2s ease;
}

.preview-area:active {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8rpx;
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  height: 100%;
  width: 100%;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
}

.empty-hint {
  font-size: 22rpx;
  color: #9ca3af;
}

/* 自定义分析区域样式 */
.analysis-card {
  display: flex;
  flex-direction: column;
}

.analysis-input-area {
  display: flex;
  align-items: center;
  background: #f5f7fa;
  border-radius: 40rpx;
  padding: 6rpx 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #eaeaea;
}

.analysis-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.send-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1677ff, #3b95f2);
  border-radius: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(22, 119, 255, 0.2);
  transition: all 0.2s;
}

.send-button:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.send-icon {
  width: 40rpx;
  height: 40rpx;
}

.prompt-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.prompt-tag {
  background: #f0f4f9;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #1677ff;
  transition: all 0.2s;
  border: 1rpx solid rgba(22, 119, 255, 0.2);
}

.prompt-tag:active {
  background: rgba(22, 119, 255, 0.1);
  transform: scale(0.95);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 24rpx;
  border-top: 1rpx solid #f1f5f9;
  gap: 12rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  flex: 1;
}

.action-btn.primary {
  background-color: #3b82f6;
  color: #ffffff;
}

.action-btn.secondary {
  background-color: #f8fafc;
  color: #64748b;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.primary:active {
  background-color: #2563eb;
}

.action-btn.secondary:active {
  background-color: #e2e8f0;
}

.btn-emoji {
  font-size: 28rpx;
}

.btn-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 进度条样式 */
.progress-container {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.progress-bar {
  width: 100%;
  height: 10rpx;
  background: #eee;
  border-radius: 10rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1677ff, #3b95f2);
  border-radius: 10rpx;
  transition: width 0.3s;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 识别结果卡片样式 */
.result-card {
  display: flex;
  flex-direction: column;
}

.result-content {
  padding: 24rpx;
  max-height: 300rpx;
}

.result-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 历史记录卡片样式 */
.history-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.history-list {
  padding: 16rpx 24rpx;
  flex: 1;
  max-height: 500rpx;
}

.history-item {
  display: flex;
  padding: 24rpx;
  background: #f8faff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  transition: all 0.2s;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.history-item:active {
  background: #f0f4f9;
  transform: translateY(2rpx);
}

.history-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  object-fit: cover;
  border: 1rpx solid rgba(0,0,0,0.05);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.history-model-tag {
  font-size: 22rpx;
  color: #1677ff;
  background: rgba(22, 119, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.history-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 12rpx;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-footer {
  display: flex;
  justify-content: flex-end;
}

.history-actions {
  display: flex;
  gap: 16rpx;
}

.history-action-icon {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.7;
  padding: 6rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
}

.history-action-icon:active {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

/* 加载遮罩样式 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: #fff;
  padding: 30rpx 60rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  animation: pulse 1.5s infinite ease-in-out;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(0.98); }
} 