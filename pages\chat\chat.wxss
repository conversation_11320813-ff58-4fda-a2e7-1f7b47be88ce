.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.navbar {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e2e8f0;
  position: relative;
  z-index: 100;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx 24rpx;
  min-height: 88rpx;
}

.navbar-title {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.2;
}

.subtitle {
  font-size: 22rpx;
  color: #64748b;
  margin-top: 2rpx;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f1f5f9;
  transition: all 0.2s ease;
}

.action-item:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

/* 模型选择器 */
.model-selector {
  gap: 6rpx;
}

.model-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
}

.selector-icon {
  font-size: 20rpx;
  color: #64748b;
}

.clear-btn {
  padding: 8rpx;
}

.clear-icon {
  font-size: 24rpx;
}

/* 模型下拉菜单 */
.model-dropdown {
  position: absolute;
  top: 100%;
  right: 24rpx;
  width: 280rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 1rpx solid #e2e8f0;
  z-index: 200;
  transform: translateY(-8rpx);
  opacity: 0;
  transition: all 0.2s ease;
  pointer-events: none;
  overflow: hidden;
}

.model-dropdown.show {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}

.dropdown-content {
  padding: 16rpx 0;
}

.dropdown-header {
  padding: 12rpx 20rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.dropdown-title {
  font-size: 24rpx;
  font-weight: 500;
  color: #374151;
}

.model-list {
  max-height: 320rpx;
  overflow-y: auto;
}

.model-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  transition: background-color 0.2s ease;
}

.model-option:active {
  background-color: #f8fafc;
}

.model-option.selected {
  background-color: #eff6ff;
}

.model-name {
  font-size: 26rpx;
  color: #374151;
  flex: 1;
}

.model-option.selected .model-name {
  color: #3b82f6;
  font-weight: 500;
}

.check-mark {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 600;
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 24rpx;
  background-color: #f8fafc;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 日期分隔符 */
.date-divider {
  text-align: center;
  margin: 16rpx 0 24rpx;
}

.date-text {
  font-size: 22rpx;
  color: #94a3b8;
  background-color: #ffffff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid #f1f5f9;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.message-item {
  display: flex;
  flex-direction: column;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(16rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 用户消息 */
.user-message {
  align-items: flex-end;
}

.user-bubble {
  max-width: 80%;
  align-self: flex-end;
  background-color: #3b82f6;
  color: #ffffff;
  padding: 16rpx 20rpx;
  border-radius: 20rpx 20rpx 4rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
}

.user-bubble .message-text {
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-all;
}

.user-bubble .message-meta {
  margin-top: 8rpx;
  text-align: right;
}

.user-bubble .time-stamp {
  font-size: 20rpx;
  opacity: 0.8;
}

/* AI消息 */
.ai-message {
  align-items: flex-start;
}

.ai-bubble {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  max-width: 85%;
}

.ai-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background-color: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-emoji {
  font-size: 24rpx;
}

.ai-content {
  flex: 1;
  background-color: #ffffff;
  padding: 16rpx 20rpx;
  border-radius: 4rpx 20rpx 20rpx 20rpx;
  border: 1rpx solid #f1f5f9;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
}

.content-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #374151;
  word-break: break-all;
}

/* 解析内容（包含代码块） */
.parsed-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.code-container {
  background-color: #1f2937;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 8rpx 0;
  border: 1rpx solid #374151;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background-color: #374151;
  border-bottom: 1rpx solid #4b5563;
}

.code-lang {
  font-size: 22rpx;
  color: #d1d5db;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  font-weight: 500;
}

.copy-code-btn {
  padding: 4rpx 12rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 6rpx;
  transition: all 0.2s ease;
}

.copy-code-btn:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.copy-text {
  font-size: 20rpx;
  color: #d1d5db;
}

.code-area {
  padding: 16rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.code-text {
  font-family: 'Courier New', Courier, monospace;
  font-size: 24rpx;
  line-height: 1.5;
  color: #e5e7eb;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 系统消息 */
.system-content {
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  margin: 8rpx 0;
  text-align: center;
}

.system-content.info {
  background-color: #dbeafe;
  border: 1rpx solid #93c5fd;
}

.system-content.warning {
  background-color: #fef3c7;
  border: 1rpx solid #fbbf24;
}

.system-text {
  font-size: 24rpx;
  color: #374151;
}

/* AI消息底部 */
.ai-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
  padding-top: 8rpx;
  border-top: 1rpx solid #f1f5f9;
}

.time-stamp {
  font-size: 20rpx;
  color: #9ca3af;
}

.ai-actions {
  display: flex;
  gap: 12rpx;
}

.action-copy {
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  background-color: #f8fafc;
  transition: all 0.2s ease;
}

.action-copy:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.action-icon {
  font-size: 20rpx;
}

/* AI正在输入指示器 */
.typing-indicator {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  margin-top: 24rpx;
  animation: fadeIn 0.3s ease-out;
}

.typing-animation {
  background-color: #ffffff;
  padding: 16rpx 20rpx;
  border-radius: 4rpx 20rpx 20rpx 20rpx;
  border: 1rpx solid #f1f5f9;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.typing-dots {
  display: flex;
  gap: 6rpx;
  align-items: center;
}

.typing-dots .dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #3b82f6;
  border-radius: 50%;
  animation: typingPulse 1.4s ease-in-out infinite;
}

.typing-dots .dot:nth-child(1) { animation-delay: 0s; }
.typing-dots .dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dots .dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typingPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: 22rpx;
  color: #9ca3af;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 输入区域 */
.input-section {
  background-color: #ffffff;
  border-top: 1rpx solid #e2e8f0;
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12rpx;
  background-color: #f8fafc;
  border-radius: 24rpx;
  padding: 8rpx;
  border: 1rpx solid #e2e8f0;
}

.input-field {
  flex: 1;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 0 16rpx;
  border: 1rpx solid #f1f5f9;
}

.text-input {
  width: 100%;
  min-height: 80rpx;
  max-height: 200rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #374151;
  padding: 16rpx 0;
}

.input-controls {
  display: flex;
  gap: 8rpx;
}

.control-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background-color: #f1f5f9;
}

.control-btn:active {
  transform: scale(0.95);
  background-color: #e2e8f0;
}

.voice-btn {
  background-color: #fef3c7;
}

.voice-btn:active {
  background-color: #fde68a;
}

.send-btn.active {
  background-color: #3b82f6;
}

.send-btn.active:active {
  background-color: #2563eb;
}

.send-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-emoji {
  font-size: 28rpx;
}

.send-btn.active .btn-emoji {
  filter: brightness(0) invert(1);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .navbar-content {
    padding: 24rpx 16rpx 20rpx;
  }

  .chat-messages {
    padding: 16rpx;
  }

  .input-section {
    padding: 16rpx;
    padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  }
}