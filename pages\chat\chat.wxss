.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(to bottom, #f0f6ff, #f5f7fa);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #1677ff 0%, #3b95f2 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  position: relative;
  z-index: 10;
}

.header-title {
  display: flex;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  margin-left: 10rpx;
  background: linear-gradient(90deg, #ffffff, #e0f0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 10rpx rgba(255,255,255,0.2);
}

.header-actions {
  display: flex;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20rpx;
  opacity: 0.9;
  transition: all 0.2s;
}

.action-btn image {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(1.2);
}

.action-btn:active {
  opacity: 0.7;
  transform: scale(0.95);
}

/* 模型选择按钮样式 */
.model-select-btn {
  width: auto;
  padding: 0 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.model-name {
  font-size: 24rpx;
  color: #ffffff;
  margin-right: 6rpx;
  max-width: 180rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  transition: transform 0.3s;
}

.arrow-icon.rotate {
  transform: rotate(180deg);
}

/* 模型选择器样式 */
.model-selector {
  position: absolute;
  top: 100rpx;
  right: 20rpx;
  width: 300rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  z-index: 100;
  overflow: hidden;
  transform: translateY(-10px);
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
}

.model-selector.show {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}

.model-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.model-item {
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.model-item:active {
  background-color: #f5f5f5;
}

.model-item.active {
  background-color: rgba(22, 119, 255, 0.05);
}

.model-item-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-selected-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 10rpx;
}

/* 系统消息样式 */
.system-message {
  background: linear-gradient(135deg, rgba(245, 247, 250, 0.9) 0%, rgba(240, 242, 245, 0.9) 100%);
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  border: 1px solid #eaeaea;
  font-size: 26rpx;
  text-align: center;
  margin: 10rpx 0;
}

.system-message.info {
  background: linear-gradient(135deg, rgba(230, 244, 255, 0.9) 0%, rgba(210, 240, 255, 0.9) 100%);
  border: 1px solid #d0e8ff;
  color: #1677ff;
}

.system-message.warning {
  background: linear-gradient(135deg, rgba(255, 248, 230, 0.9) 0%, rgba(255, 243, 210, 0.9) 100%);
  border: 1px solid #ffe7ba;
  color: #fa8c16;
}

.chat-content {
  flex: 1;
  padding: 30rpx 24rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(100, 130, 255, 0.03) 0%, transparent 70%),
    radial-gradient(circle at 90% 80%, rgba(120, 200, 255, 0.05) 0%, transparent 70%);
}

.chat-date-divider {
  text-align: center;
  margin: 20rpx 0 30rpx;
}

.chat-date-divider text {
  font-size: 24rpx;
  color: #8a9aa9;
  background: linear-gradient(90deg, rgba(255,255,255,0.5), rgba(255,255,255,0.9), rgba(255,255,255,0.5));
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.message {
  display: flex;
  margin-bottom: 40rpx;
  position: relative;
  transition: transform 0.3s ease, opacity 0.3s ease;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from { 
    opacity: 0; 
    transform: translateY(20rpx); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.message-user {
  flex-direction: row-reverse;
}

.avatar-container {
  margin: 0 12rpx;
  position: relative;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f0f0f0;
  border: 2rpx solid rgba(255,255,255,0.8);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.message:hover .avatar {
  transform: scale(1.05);
}

.message-body {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  position: relative;
  word-break: break-all;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.message-user .message-bubble {
  background: linear-gradient(135deg, #0068ff 0%, #2483ff 100%);
  color: white;
  border-radius: 20rpx 4rpx 20rpx 20rpx;
  margin-right: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,104,255,0.2);
}

.message-ai .message-bubble {
  background: linear-gradient(135deg, #ffffff 0%, #f5f9ff 100%);
  color: #333;
  border-radius: 4rpx 20rpx 20rpx 20rpx;
  margin-left: 10rpx;
  border: 1rpx solid rgba(0,0,0,0.05);
}

.text-content {
  font-size: 30rpx;
  line-height: 1.6;
}

.content-blocks {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.code-block {
  background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
  border-radius: 12rpx;
  overflow: hidden;
  margin: 16rpx 0;
  border: 1rpx solid rgba(255,255,255,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.code-block:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.12);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: linear-gradient(to right, #2d2d2d, #353535);
  border-bottom: 1rpx solid rgba(255,255,255,0.1);
}

.lang-label {
  color: #e6e6e6;
  font-size: 24rpx;
  font-family: monospace;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.copy-button {
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.1);
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.2s;
}

.copy-button image {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.copy-button text {
  font-size: 24rpx;
  color: #e6e6e6;
}

.copy-button:active {
  background: rgba(255,255,255,0.2);
  transform: scale(0.95);
}

.code-scroll {
  max-width: 100%;
  white-space: nowrap;
  position: relative;
}

.code-content {
  padding: 20rpx;
  color: #e6e6e6;
  font-family: 'Courier New', Courier, monospace;
  font-size: 28rpx;
  line-height: 1.6;
  white-space: pre-wrap;
  text-align: left;
  display: block;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
  padding: 0 10rpx;
}

.message-time {
  font-size: 24rpx;
  color: #8a9aa9;
}

.message-user .message-footer {
  flex-direction: row-reverse;
}

.message-actions {
  display: flex;
  gap: 20rpx;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.message:hover .message-actions {
  opacity: 1;
}

.action-item {
  width: 48rpx;
  height: 48rpx;
  background: white;
  border-radius: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  transition: all 0.2s;
}

.action-item image {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.7;
}

.action-item:active {
  transform: scale(1.1);
  background: #f5f5f5;
}

.ai-typing {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
}

.typing-indicator {
  background: linear-gradient(135deg, #ffffff 0%, #f5f9ff 100%);
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  background: linear-gradient(135deg, #1677ff, #3b95f2);
  border-radius: 50%;
  margin: 0 6rpx;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 100% { transform: translateY(0); opacity: 0.5; }
  50% { transform: translateY(-8rpx); opacity: 1; }
}

.input-area {
  background: linear-gradient(180deg, #f5f7fa, #ffffff);
  border-top: 1rpx solid #eaeaea;
  padding: 20rpx 24rpx;
}

.input-container {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 40rpx;
  padding: 4rpx 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
  border: 1rpx solid rgba(22, 119, 255, 0.1);
}

.message-input {
  flex: 1;
  height: 76rpx;
  font-size: 30rpx;
  padding: 0 20rpx;
}

.input-actions {
  display: flex;
  align-items: center;
}

.voice-btn, .send-btn {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 36rpx;
  margin-left: 10rpx;
  transition: all 0.2s;
}

.voice-btn image, .send-btn image {
  width: 44rpx;
  height: 44rpx;
  opacity: 0.7;
}

.voice-btn:active {
  background: rgba(0,0,0,0.05);
}

.send-btn {
  background: linear-gradient(135deg, #1677ff 0%, #3b95f2 100%);
}

.send-btn image {
  opacity: 1;
  filter: brightness(10);
}

.send-btn.disabled {
  background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 100%);
}

.send-btn.disabled image {
  opacity: 0.4;
}

.send-btn.active:active {
  transform: scale(0.95);
} 