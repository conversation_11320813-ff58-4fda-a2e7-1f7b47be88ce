# 微信小程序音乐功能使用说明

## 问题排查：音乐搜索不可用

如果您遇到音乐搜索功能不可用的问题，可能是因为小程序尝试连接到Ollama API (端口11434)而不是音乐服务API (端口5000)。

## 解决方案

1. **修改配置文件**：已更新`config.js`，添加了专门的音乐服务API地址：
   ```js
   musicApiBaseUrl: 'http://127.0.0.1:5000'  // 本地音乐服务端口是5000
   ```

2. **启动音乐服务**：
   - 双击运行`start_music_service.bat`启动音乐服务
   - 或者直接运行`python simple_local_music.py`

3. **添加音乐**：
   - 首次使用时，运行`add_music.bat`添加本地音乐文件
   - 按照提示选择MP3文件并输入歌曲信息

## 技术说明

1. **服务架构**：
   - AI聊天功能使用Ollama服务（端口11434）
   - 音乐功能使用本地音乐服务（端口5000）
   - 两个服务需要分别启动

2. **文件说明**：
   - `simple_local_music.py`：本地音乐服务主程序
   - `add_local_music.py`：添加本地音乐到索引的工具
   - `start_music_service.bat`：启动音乐服务的批处理脚本
   - `add_music.bat`：启动音乐添加工具的批处理脚本

3. **端口使用**：
   - 5000：音乐服务端口
   - 11434：Ollama API端口

## 常见问题

1. **搜索没有结果**：
   - 确保已经添加了音乐文件
   - 检查`download/music_index.json`是否存在且包含音乐信息

2. **服务无法启动**：
   - 检查端口5000是否被占用
   - 确保已安装Python及所需库
   - 检查控制台错误信息

3. **手机无法连接**：
   - 确保手机和电脑在同一网络
   - 在`config.js`中使用电脑的实际IP地址而非localhost
   - 检查防火墙是否阻止了端口5000的访问

## 音乐搜索和播放功能说明

1. **搜索功能**：支持按歌曲名、歌手名和专辑名搜索

2. **播放功能**：
   - 支持预览模式（较小文件，适合微信小程序）
   - 支持在线流式播放
   - 自动转码大文件以符合微信小程序限制

3. **封面生成**：如果歌曲没有封面，会自动生成简单封面

## 开发和调试

1. 可以在浏览器中访问`http://127.0.0.1:5000/api/status`检查音乐服务状态

2. 日志信息将显示在命令行窗口中，有助于调试 