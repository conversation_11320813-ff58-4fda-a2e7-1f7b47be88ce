/* 全局容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
}

/* 顶部标题区域 */
.header {
  padding: 40rpx 30rpx 20rpx;
  background: linear-gradient(135deg, #4a6bff, #2541b8);
  color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.title-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.title {
  font-size: 38rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 网络状态提示 */
.network-status {
  position: absolute;
  top: 40rpx;
  right: 30rpx;
  display: flex;
  align-items: center;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(5rpx);
}

.network-status.online {
  background: rgba(39, 194, 76, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(39, 194, 76, 0.2);
}

.network-status.offline {
  background: rgba(255, 152, 0, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.2);
}

.network-status text {
  margin-left: 8rpx;
}

/* 离线提示 */
.offline-tip {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #fff8e1;
  border-left: 8rpx solid #ff9800;
  margin: 16rpx 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.1);
  animation: slideDown 0.5s ease;
}

@keyframes slideDown {
  from { transform: translateY(-20rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.offline-tip text {
  margin-left: 12rpx;
  font-size: 26rpx;
  color: #ff9800;
}

/* 游客模式提示样式 */
.guest-mode-tip {
  position: absolute;
  top: 40rpx;
  left: 30rpx;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 10rpx 16rpx;
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.guest-tip-text {
  display: flex;
  flex-direction: column;
  margin-left: 8rpx;
}

.guest-label {
  font-size: 24rpx;
  font-weight: 500;
}

.guest-tip-detail {
  font-size: 20rpx;
  opacity: 0.8;
}

/* 标签页导航 */
.tabs-container {
  background-color: #fff;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 9;
  border-radius: 0;
  overflow: hidden;
}

.tabs {
  display: flex;
  white-space: nowrap;
  padding: 0;
  background-color: #fff;
}

.tab {
  position: relative;
  padding: 24rpx 36rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
  display: inline-block;
}

.tab.active {
  color: #4a6bff;
  font-weight: 500;
}

/* 标签数量样式 */
.tab-count {
  display: inline-block;
  font-size: 20rpx;
  background-color: #f0f4ff;
  color: #4a6bff;
  border-radius: 20rpx;
  padding: 2rpx 10rpx;
  margin-left: 8rpx;
  min-width: 24rpx;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
  transform: translateY(-6rpx);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.tab.active .tab-count {
  background-color: #4a6bff;
  color: #fff;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 30%;
  right: 30%;
  height: 4rpx;
  background-color: #4a6bff;
  border-radius: 4rpx;
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scaleX(0.5); }
  to { opacity: 1; transform: scaleX(1); }
}

/* 最后更新时间 */
.refresh-time {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  font-size: 24rpx;
  color: #999;
  background-color: transparent;
  margin: 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.refresh-time text {
  margin-left: 8rpx;
}

/* 历史记录列表 */
.history-list {
  flex: 1;
  padding: 20rpx 30rpx;
  position: relative;
  overflow: hidden;
  background-color: #f5f7fa;
}

.history-item {
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  display: flex;
  transition: all 0.3s ease;
  transform: translateZ(0);
  animation: slideIn 0.3s ease-out;
  position: relative;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.item-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.item-content {
  flex: 1;
  padding: 24rpx;
  position: relative;
  z-index: 1;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

/* 类型标签样式 - 完全重新设计 */
.type-tag {
  display: inline-flex;
  align-items: center;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  color: #fff;
}

.type-text {
  font-size: 22rpx;
  font-weight: 500;
}

.type-tag.translate {
  background-color: #13c2c2;
}

.type-tag.chat {
  background-color: #1677ff;
}

.type-tag.image {
  background-color: #722ed1;
}

.type-tag.voice {
  background-color: #eb2f96;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.content {
  display: flex;
  flex-direction: column;
}

.query {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
  line-height: 1.4;
  word-break: break-all;
}

.result {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
}

/* 图片预览 */
.image-preview {
  margin-top: 16rpx;
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 模型标签 - 完全重新设计 */
.model-tag {
  display: inline-block;
  margin-top: 16rpx;
  background-color: #f0f4ff;
  border-radius: 4rpx;
  padding: 4rpx 12rpx;
  font-size: 22rpx;
  color: #4a6bff;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.actions {
  width: 80rpx;
  display: flex;
  flex-direction: column;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn.delete {
  background-color: #fff2f0;
}

.action-btn.delete:active {
  background-color: #ffccc7;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  animation: fadeIn 0.5s ease;
}

.empty-title {
  font-size: 34rpx;
  color: #666;
  margin-top: 30rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.try-btn {
  background: linear-gradient(135deg, #4a6bff, #2541b8);
  color: #fff;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(74, 107, 255, 0.3);
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

.try-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(74, 107, 255, 0.2);
}

/* 加载中状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4a6bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 28rpx;
  color: #999;
}

/* 底部操作区 */
.footer-actions {
  padding: 24rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background: linear-gradient(135deg, #ff7875, #ff4d4f);
  color: #fff;
  border-radius: 44rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
  transition: all 0.3s ease;
}

.btn-hover {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.2);
}

.clear-btn text {
  margin-left: 8rpx;
}

/* 游客模式底部提示 */
.footer-guest-tip {
  padding: 24rpx 30rpx;
  background-color: #fff;
  text-align: center;
  font-size: 26rpx;
  color: #999;
  border-top: 1rpx solid #f0f0f0;
}

.login-link {
  color: #4a6bff;
  text-decoration: underline;
  margin-left: 16rpx;
  display: inline-block;
  margin-top: 10rpx;
  transition: all 0.3s ease;
}

.login-link:active {
  opacity: 0.8;
}

/* 标签页悬停效果 */
.tab-hover {
  background-color: rgba(74, 107, 255, 0.05);
}

/* 修复undefined显示问题 */
.query:empty::before,
.result:empty::before {
  content: "无内容";
  color: #ccc;
  font-style: italic;
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

/* 动画效果增强 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}