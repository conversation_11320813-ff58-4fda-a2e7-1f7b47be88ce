<view class="container">
  <!-- 顶部用户信息区域 -->
  <view class="profile-section">
    <view class="profile-card">
      <view class="avatar-wrapper">
        <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="avatar-status"></view>
      </view>
      <view class="user-details">
        <text class="username">{{userInfo.nickName || '未登录'}}</text>
        <text class="user-role">AI助手用户</text>
        <view class="user-stats">
          <view class="stat-item">
            <text class="stat-number">{{totalChats || 0}}</text>
            <text class="stat-label">对话</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{totalDays || 0}}</text>
            <text class="stat-label">天数</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <scroll-view scroll-y class="main-content" enhanced show-scrollbar="{{false}}">
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="section-header">
        <text class="section-title">功能菜单</text>
      </view>

      <view class="menu-grid">
        <navigator url="/pages/history/history" class="menu-card" hover-class="menu-hover">
          <view class="menu-icon">
            <text class="icon-emoji">📝</text>
          </view>
          <text class="menu-title">使用记录</text>
          <text class="menu-desc">查看历史记录</text>
        </navigator>

        <navigator url="/pages/chat/chat" class="menu-card" hover-class="menu-hover">
          <view class="menu-icon">
            <text class="icon-emoji">💬</text>
          </view>
          <text class="menu-title">AI对话</text>
          <text class="menu-desc">智能聊天助手</text>
        </navigator>

        <view class="menu-card" hover-class="menu-hover" bindtap="navigateToSettings">
          <view class="menu-icon">
            <text class="icon-emoji">⚙️</text>
          </view>
          <text class="menu-title">设置</text>
          <text class="menu-desc">个性化配置</text>
        </view>

        <view class="menu-card" hover-class="menu-hover" bindtap="navigateToAbout">
          <view class="menu-icon">
            <text class="icon-emoji">ℹ️</text>
          </view>
          <text class="menu-title">关于</text>
          <text class="menu-desc">应用信息</text>
        </view>
      </view>
    </view>

    <!-- 其他选项 -->
    <view class="options-section">
      <view class="section-header">
        <text class="section-title">其他</text>
      </view>

      <view class="options-list">
        <view class="option-item" bindtap="clearCache">
          <view class="option-content">
            <text class="option-icon">🗑️</text>
            <text class="option-text">清除缓存</text>
          </view>
          <text class="option-arrow">›</text>
        </view>

        <view class="option-item" bindtap="checkUpdate">
          <view class="option-content">
            <text class="option-icon">🔄</text>
            <text class="option-text">检查更新</text>
          </view>
          <text class="option-arrow">›</text>
        </view>

        <view class="option-item" bindtap="feedback">
          <view class="option-content">
            <text class="option-icon">💌</text>
            <text class="option-text">意见反馈</text>
          </view>
          <text class="option-arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section" wx:if="{{userInfo.nickName}}">
      <view class="logout-btn" bindtap="handleLogout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer-section">
      <text class="version-info">版本 1.0.0</text>
      <text class="copyright">AI助手 © 2025</text>
    </view>
  </scroll-view>
</view>