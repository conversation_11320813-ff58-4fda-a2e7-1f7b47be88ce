.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.navbar {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e2e8f0;
  position: relative;
  z-index: 100;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx 24rpx;
  min-height: 88rpx;
}

.navbar-title {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.2;
}

.subtitle {
  font-size: 22rpx;
  color: #64748b;
  margin-top: 2rpx;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f1f5f9;
  transition: all 0.2s ease;
}

.model-selector:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.model-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
}

.selector-icon {
  font-size: 20rpx;
}

/* 语言选择区域 */
.language-section {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e2e8f0;
  padding: 24rpx;
}

.language-selectors {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.language-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 16rpx;
  background-color: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  transition: all 0.2s ease;
}

.language-item:active {
  background-color: #f1f5f9;
  transform: scale(0.98);
}

.language-label {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

.language-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.language-name {
  font-size: 26rpx;
  color: #1a202c;
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 20rpx;
  color: #64748b;
}

.switch-languages {
  padding: 16rpx;
  background-color: #3b82f6;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.switch-languages:active {
  background-color: #2563eb;
  transform: scale(0.95);
}

.switch-icon {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 24rpx;
  background-color: #f8fafc;
}

/* 输入区域样式 */
.input-area {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.input-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.input-text {
  width: 100%;
  height: 200rpx;
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  padding: 0;
}

.input-tools {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.tool-button {
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.8;
}

.tool-icon.recording {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.word-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 翻译结果区域样式 */
.result-area {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.result-tools {
  display: flex;
  gap: 16rpx;
}

.result-content {
  padding: 10rpx 0;
}

.result-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
}

/* 历史记录区域样式 */
.history-area {
  flex: 1;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
}

.history-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.clear-btn {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  background: #f5f7fa;
  border-radius: 12rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.history-item:active {
  background: #eef2f8;
}

.history-languages {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.language-direction {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.history-actions {
  display: flex;
}

.history-action-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 历史记录底部样式 */
.history-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

/* 历史记录中的模型标签样式 */
.history-model {
  font-size: 22rpx;
  color: #3A7FED;
  background: rgba(58, 127, 237, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.history-source {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.history-result {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.empty-history-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 40rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

/* 语言选择弹窗样式 */
.language-picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  align-items: flex-end;
}

.language-picker {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  padding-bottom: env(safe-area-inset-bottom);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
  padding: 10rpx;
}

.language-list {
  max-height: calc(80vh - 200rpx);
}

.language-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.language-item.selected {
  color: #3A7FED;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 加载遮罩样式 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  background: #fff;
  padding: 30rpx 60rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
} 