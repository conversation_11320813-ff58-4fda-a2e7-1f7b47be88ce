.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.navbar {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e2e8f0;
  position: relative;
  z-index: 100;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx 24rpx;
  min-height: 88rpx;
}

.navbar-title {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.2;
}

.subtitle {
  font-size: 22rpx;
  color: #64748b;
  margin-top: 2rpx;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f1f5f9;
  transition: all 0.2s ease;
}

.model-selector:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.model-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
}

.selector-icon {
  font-size: 20rpx;
}

/* 语言选择区域 */
.language-section {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e2e8f0;
  padding: 24rpx;
}

.language-selectors {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.language-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 16rpx;
  background-color: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  transition: all 0.2s ease;
}

.language-item:active {
  background-color: #f1f5f9;
  transform: scale(0.98);
}

.language-label {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

.language-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.language-name {
  font-size: 26rpx;
  color: #1a202c;
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 20rpx;
  color: #64748b;
}

.switch-languages {
  padding: 16rpx;
  background-color: #3b82f6;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.switch-languages:active {
  background-color: #2563eb;
  transform: scale(0.95);
}

.switch-icon {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 24rpx;
  background-color: #f8fafc;
}

/* 卡片样式 */
.input-card, .result-card, .history-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f1f5f9;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.card-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a202c;
}

.card-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background-color: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.voice-btn {
  background-color: #fef3c7;
}

.voice-btn.recording {
  background-color: #fbbf24;
  animation: recordingPulse 1s infinite;
}

@keyframes recordingPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.btn-emoji {
  font-size: 24rpx;
}

/* 输入区域 */
.input-area {
  padding: 24rpx;
}

.input-textarea {
  width: 100%;
  min-height: 160rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #374151;
  background-color: transparent;
  border: none;
  outline: none;
}

.input-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  border-top: 1rpx solid #f1f5f9;
}

.char-count {
  font-size: 22rpx;
  color: #9ca3af;
}

.translate-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  background-color: #f1f5f9;
  transition: all 0.2s ease;
}

.translate-btn.active {
  background-color: #3b82f6;
  color: #ffffff;
}

.translate-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.translate-btn:active {
  transform: scale(0.95);
}

.btn-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 结果内容 */
.result-content {
  padding: 24rpx;
}

.result-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
  word-break: break-all;
}

/* 历史记录 */
.history-list {
  padding: 0 24rpx 24rpx;
}

.history-item {
  background-color: #f8fafc;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #f1f5f9;
  transition: all 0.2s ease;
}

.history-item:active {
  background-color: #f1f5f9;
  transform: scale(0.98);
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.language-pair {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.history-actions {
  display: flex;
  gap: 8rpx;
}

.history-action {
  padding: 6rpx;
  border-radius: 6rpx;
  background-color: #ffffff;
  transition: all 0.2s ease;
}

.history-action:active {
  background-color: #e2e8f0;
  transform: scale(0.9);
}

.action-emoji {
  font-size: 20rpx;
}

.history-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.source-text {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.4;
  word-break: break-all;
}

.result-text {
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.4;
  word-break: break-all;
}

.history-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12rpx;
  padding-top: 8rpx;
  border-top: 1rpx solid #f1f5f9;
}

.history-time {
  font-size: 20rpx;
  color: #9ca3af;
}

.history-model {
  font-size: 20rpx;
  color: #3b82f6;
  background-color: #eff6ff;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #374151;
  margin-bottom: 12rpx;
  font-weight: 600;
}

.empty-desc {
  font-size: 26rpx;
  color: #9ca3af;
  line-height: 1.5;
}

/* 弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  width: 85%;
  max-height: 70%;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 28rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background-color: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.close-icon {
  font-size: 24rpx;
  color: #64748b;
}

.language-options {
  max-height: 500rpx;
  padding: 8rpx 0;
}

.language-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 28rpx;
  transition: background-color 0.2s ease;
}

.language-option:active {
  background-color: #f8fafc;
}

.language-option.selected {
  background-color: #eff6ff;
}

.option-text {
  font-size: 28rpx;
  color: #374151;
  flex: 1;
}

.language-option.selected .option-text {
  color: #3b82f6;
  font-weight: 500;
}

.check-mark {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 600;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.loading-spinner {
  background-color: #ffffff;
  padding: 40rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.spinner-dots {
  display: flex;
  gap: 8rpx;
}

.spinner-dots .dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #3b82f6;
  border-radius: 50%;
  animation: spinnerPulse 1.4s ease-in-out infinite;
}

.spinner-dots .dot:nth-child(1) { animation-delay: 0s; }
.spinner-dots .dot:nth-child(2) { animation-delay: 0.2s; }
.spinner-dots .dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes spinnerPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-text {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .navbar-content {
    padding: 24rpx 16rpx 20rpx;
  }

  .language-section {
    padding: 16rpx;
  }

  .main-content {
    padding: 16rpx;
  }

  .modal-content {
    width: 90%;
  }
}